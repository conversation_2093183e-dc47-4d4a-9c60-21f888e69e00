import { renderHook, act } from '@testing-library/react';
import { useWealthTracker } from '../hooks/useWealthTracker';
import { mockApi } from '../services/mockApi';

// Mock the apiService
jest.mock('../services/mockApi', () => ({
  mockApi: {
    getWealthData: jest.fn(),
    getWealthAnalysis: jest.fn(),
    getFinancialProjections: jest.fn(),
    addPortfolioData: jest.fn(),
    addPayslipData: jest.fn(),
    resetData: jest.fn(),
  }
}));

const mockedMockApi = mockApi as jest.Mocked<typeof mockApi>;

describe('useWealthTracker Hook Comprehensive Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Set up default mock responses
    mockedMockApi.getWealthData.mockResolvedValue({
      success: true,
      data: {
        portfolioData: [],
        payslipData: [],
      },
    });

    mockedMockApi.getWealthAnalysis.mockResolvedValue({
      success: true,
      data: {
        totalAssets: 800000,
        monthlyIncome: 6000,
        annualIncome: 72000,
        savingsRate: 0.2,
        netWorth: 750000,
        assetBreakdown: {
          stocks: 500000,
          bonds: 200000,
          savings: 100000
        }
      },
    });

    mockedMockApi.getFinancialProjections.mockResolvedValue({
      success: true,
      data: [],
    });
  });

  describe('Initial State', () => {
    it('should initialize with correct default state', () => {
      const { result } = renderHook(() => useWealthTracker());
      
      expect(result.current.wealthData).toBeNull();
      expect(result.current.analysis).toBeNull();
      expect(result.current.projections).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(typeof result.current.loadWealthData).toBe('function');
      expect(typeof result.current.clearError).toBe('function');
      expect(typeof result.current.analyzeWealth).toBe('function');
      expect(typeof result.current.generateProjections).toBe('function');
    });
  });

  describe('loadWealthData', () => {
    it('should load wealth data successfully', async () => {
      const mockData = {
        portfolioData: [
          { assetType: 'stocks' as const, value: 100000, description: 'Test stocks' },
        ],
        payslipData: [
          {
            grossSalary: 8000,
            netSalary: 6000,
            taxDeductions: 1500,
            benefits: 500,
            payPeriod: 'monthly' as const
          },
        ],
      };
      
      mockedMockApi.getWealthData.mockResolvedValue({
        success: true,
        data: mockData,
      });

      const { result } = renderHook(() => useWealthTracker());

      await act(async () => {
        await result.current.loadWealthData();
      });

      expect(result.current.wealthData).toEqual(mockData);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(mockedMockApi.getWealthData).toHaveBeenCalledTimes(1);
    });

    it('should set loading state during data fetch', async () => {
      const { result } = renderHook(() => useWealthTracker());
      
      let loadingState: boolean | undefined;
      
      act(() => {
        result.current.loadWealthData().then(() => {
          // Loading should be false after completion
        });
        loadingState = result.current.isLoading;
      });
      
      expect(loadingState).toBe(true);
      
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });
      
      expect(result.current.isLoading).toBe(false);
    });

    it('should handle API errors', async () => {
      const errorMessage = 'Failed to load wealth data';
      mockedMockApi.getWealthData.mockResolvedValue({
        success: false,
        error: errorMessage,
      });
      
      const { result } = renderHook(() => useWealthTracker());
      
      await act(async () => {
        await result.current.loadWealthData();
      });
      
      expect(result.current.wealthData).toBeNull();
      expect(result.current.error).toBe(errorMessage);
      expect(result.current.isLoading).toBe(false);
    });

    it('should handle network errors', async () => {
      mockedMockApi.getWealthData.mockRejectedValue(new Error('Network error'));
      
      const { result } = renderHook(() => useWealthTracker());
      
      await act(async () => {
        await result.current.loadWealthData();
      });
      
      expect(result.current.wealthData).toBeNull();
      expect(result.current.error).toBe('Failed to load wealth data');
      expect(result.current.isLoading).toBe(false);
    });

    it('should clear existing error when loading new data', async () => {
      const { result } = renderHook(() => useWealthTracker());
      
      // First, set an error
      mockedMockApi.getWealthData.mockResolvedValue({
        success: false,
        error: 'Initial error',
      });
      
      await act(async () => {
        await result.current.loadWealthData();
      });
      
      expect(result.current.error).toBe('Initial error');
      
      // Then, load successfully
      mockedMockApi.getWealthData.mockResolvedValue({
        success: true,
        data: { portfolioData: [], payslipData: [] },
      });
      
      await act(async () => {
        await result.current.loadWealthData();
      });
      
      expect(result.current.error).toBeNull();
    });
  });

  describe('analyzeWealth', () => {
    it('should analyze wealth successfully', async () => {
      const mockAnalysis = {
        totalAssets: 800000,
        monthlyIncome: 6000,
        annualIncome: 72000,
        savingsRate: 0.2,
        netWorth: 750000,
        assetBreakdown: {
          stocks: 500000,
          bonds: 200000,
          savings: 100000
        }
      };
      
      mockedMockApi.getWealthAnalysis.mockResolvedValue({
        success: true,
        data: mockAnalysis,
      });
      
      const { result } = renderHook(() => useWealthTracker());
      
      let returnValue: boolean | undefined;
      
      await act(async () => {
        returnValue = await result.current.analyzeWealth();
      });
      
      expect(returnValue).toBe(true);
      expect(result.current.analysis).toEqual(mockAnalysis);
      expect(result.current.error).toBeNull();
      expect(mockedMockApi.getWealthAnalysis).toHaveBeenCalledTimes(1);
    });

    it('should set loading state during analysis', async () => {
      const { result } = renderHook(() => useWealthTracker());
      
      let loadingState: boolean | undefined;
      
      act(() => {
        result.current.analyzeWealth();
        loadingState = result.current.isLoading;
      });
      
      expect(loadingState).toBe(true);
      
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });
      
      expect(result.current.isLoading).toBe(false);
    });

    it('should handle analysis errors', async () => {
      const errorMessage = 'Analysis failed';
      mockedMockApi.getWealthAnalysis.mockResolvedValue({
        success: false,
        error: errorMessage,
      });
      
      const { result } = renderHook(() => useWealthTracker());
      
      let returnValue: boolean | undefined;
      
      await act(async () => {
        returnValue = await result.current.analyzeWealth();
      });
      
      expect(returnValue).toBe(false);
      expect(result.current.analysis).toBeNull();
      expect(result.current.error).toBe(errorMessage);
    });

    it('should handle network errors during analysis', async () => {
      mockedMockApi.getWealthAnalysis.mockRejectedValue(new Error('Network error'));
      
      const { result } = renderHook(() => useWealthTracker());
      
      let returnValue: boolean | undefined;
      
      await act(async () => {
        returnValue = await result.current.analyzeWealth();
      });
      
      expect(returnValue).toBe(false);
      expect(result.current.analysis).toBeNull();
      expect(result.current.error).toBe('Failed to analyze wealth data');
    });

    it('should clear existing error when starting new analysis', async () => {
      const { result } = renderHook(() => useWealthTracker());
      
      // Set initial error
      act(() => {
        (result.current as any).setError('Initial error');
      });
      
      // Start analysis
      await act(async () => {
        await result.current.analyzeWealth();
      });
      
      // Error should be cleared even if analysis succeeds
      expect(result.current.error).toBeNull();
    });
  });

  describe('generateProjections', () => {
    it('should generate projections successfully', async () => {
      const mockProjections = [
        {
          year: 1,
          projectedWealth: 100000,
          projectedIncome: 72000,
          projectedSavings: 14400,
          projectedExpenses: 57600
        },
        {
          year: 2,
          projectedWealth: 120000,
          projectedIncome: 74160,
          projectedSavings: 14832,
          projectedExpenses: 59328
        },
        {
          year: 3,
          projectedWealth: 150000,
          projectedIncome: 76385,
          projectedSavings: 15277,
          projectedExpenses: 61108
        },
      ];
      
      mockedMockApi.getFinancialProjections.mockResolvedValue({
        success: true,
        data: mockProjections,
      });
      
      const { result } = renderHook(() => useWealthTracker());
      
      let returnValue: boolean | undefined;
      
      await act(async () => {
        returnValue = await result.current.generateProjections(24);
      });
      
      expect(returnValue).toBe(true);
      expect(result.current.projections).toEqual(mockProjections);
      expect(result.current.error).toBeNull();
      expect(mockedMockApi.getFinancialProjections).toHaveBeenCalledWith(24);
    });

    it('should generate projections with specified years', async () => {
      const { result } = renderHook(() => useWealthTracker());

      await act(async () => {
        await result.current.generateProjections(10);
      });

      expect(mockedMockApi.getFinancialProjections).toHaveBeenCalledWith(10);
    });

    it('should set loading state during projection generation', async () => {
      const { result } = renderHook(() => useWealthTracker());
      
      let loadingState: boolean | undefined;
      
      act(() => {
        result.current.generateProjections(12);
        loadingState = result.current.isLoading;
      });
      
      expect(loadingState).toBe(true);
      
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });
      
      expect(result.current.isLoading).toBe(false);
    });

    it('should handle projection generation errors', async () => {
      const errorMessage = 'Projection generation failed';
      mockedMockApi.getFinancialProjections.mockResolvedValue({
        success: false,
        error: errorMessage,
      });
      
      const { result } = renderHook(() => useWealthTracker());
      
      let returnValue: boolean | undefined;
      
      await act(async () => {
        returnValue = await result.current.generateProjections(12);
      });
      
      expect(returnValue).toBe(false);
      expect(result.current.projections).toBeNull();
      expect(result.current.error).toBe(errorMessage);
    });

    it('should handle network errors during projection generation', async () => {
      mockedMockApi.getFinancialProjections.mockRejectedValue(new Error('Network error'));
      
      const { result } = renderHook(() => useWealthTracker());
      
      let returnValue: boolean | undefined;
      
      await act(async () => {
        returnValue = await result.current.generateProjections(12);
      });
      
      expect(returnValue).toBe(false);
      expect(result.current.projections).toBeNull();
      expect(result.current.error).toBe('Failed to generate projections');
    });
  });

  describe('clearError', () => {
    it('should clear the error', () => {
      const { result } = renderHook(() => useWealthTracker());
      
      // Set an error first
      act(() => {
        (result.current as any).setError('Test error');
      });
      
      expect(result.current.error).toBe('Test error');
      
      // Clear the error
      act(() => {
        result.current.clearError();
      });
      
      expect(result.current.error).toBeNull();
    });

    it('should be safe to call when no error exists', () => {
      const { result } = renderHook(() => useWealthTracker());
      
      expect(result.current.error).toBeNull();
      
      act(() => {
        result.current.clearError();
      });
      
      expect(result.current.error).toBeNull();
    });
  });

  describe('Complex Scenarios', () => {
    it('should handle rapid successive calls', async () => {
      const { result } = renderHook(() => useWealthTracker());
      
      // Make multiple rapid calls
      const promises = [
        result.current.loadWealthData(),
        result.current.analyzeWealth(),
        result.current.generateProjections(12),
      ];
      
      await act(async () => {
        await Promise.all(promises);
      });
      
      // All calls should complete without interfering with each other
      expect(mockedMockApi.getWealthData).toHaveBeenCalledTimes(1);
      expect(mockedMockApi.getWealthAnalysis).toHaveBeenCalledTimes(1);
      expect(mockedMockApi.getFinancialProjections).toHaveBeenCalledTimes(1);
    });

    it('should maintain state consistency across multiple operations', async () => {
      const mockData = {
        portfolioData: [{ assetType: 'stocks' as const, value: 100000, description: 'Test stocks' }],
        payslipData: [{
          grossSalary: 8000,
          netSalary: 6000,
          taxDeductions: 1500,
          benefits: 500,
          payPeriod: 'monthly' as const
        }],
      };
      
      const mockAnalysis = {
        totalAssets: 800000,
        monthlyIncome: 6000,
        annualIncome: 72000,
        savingsRate: 0.2,
        netWorth: 750000,
        assetBreakdown: {
          stocks: 500000,
          bonds: 200000,
          savings: 100000
        }
      };
      
      const mockProjections = [
        {
          year: 1,
          projectedWealth: 100000,
          projectedIncome: 72000,
          projectedSavings: 14400,
          projectedExpenses: 57600
        },
      ];
      
      mockedMockApi.getWealthData.mockResolvedValue({
        success: true,
        data: mockData,
      });
      
      mockedMockApi.getWealthAnalysis.mockResolvedValue({
        success: true,
        data: mockAnalysis,
      });
      
      mockedMockApi.getFinancialProjections.mockResolvedValue({
        success: true,
        data: mockProjections,
      });
      
      const { result } = renderHook(() => useWealthTracker());
      
      // Perform operations in sequence
      await act(async () => {
        await result.current.loadWealthData();
      });
      
      expect(result.current.wealthData).toEqual(mockData);
      
      await act(async () => {
        await result.current.analyzeWealth();
      });
      
      expect(result.current.wealthData).toEqual(mockData);
      expect(result.current.analysis).toEqual(mockAnalysis);
      
      await act(async () => {
        await result.current.generateProjections(12);
      });
      
      expect(result.current.wealthData).toEqual(mockData);
      expect(result.current.analysis).toEqual(mockAnalysis);
      expect(result.current.projections).toEqual(mockProjections);
    });

    it('should handle partial failures gracefully', async () => {
      const mockData = {
        portfolioData: [{ assetType: 'stocks' as const, value: 100000, description: 'Test stocks' }],
        payslipData: [],
      };
      
      mockedMockApi.getWealthData.mockResolvedValue({
        success: true,
        data: mockData,
      });
      
      mockedMockApi.getWealthAnalysis.mockResolvedValue({
        success: false,
        error: 'Insufficient data for analysis',
      });
      
      const { result } = renderHook(() => useWealthTracker());
      
      // Load data successfully
      await act(async () => {
        await result.current.loadWealthData();
      });
      
      expect(result.current.wealthData).toEqual(mockData);
      expect(result.current.error).toBeNull();
      
      // Analysis fails
      await act(async () => {
        await result.current.analyzeWealth();
      });
      
      expect(result.current.wealthData).toEqual(mockData); // Should still have data
      expect(result.current.analysis).toBeNull();
      expect(result.current.error).toBe('Insufficient data for analysis');
    });

    it('should handle concurrent loading states properly', async () => {
      const { result } = renderHook(() => useWealthTracker());
      
      // Start multiple async operations
      const loadDataPromise = act(async () => {
        await result.current.loadWealthData();
      });
      
      const analyzePromise = act(async () => {
        await result.current.analyzeWealth();
      });
      
      // Both should complete without race conditions
      await Promise.all([loadDataPromise, analyzePromise]);
      
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('Memory Management', () => {
    it('should not cause memory leaks with rapid state updates', async () => {
      const { result, unmount } = renderHook(() => useWealthTracker());
      
      // Perform multiple operations
      for (let i = 0; i < 5; i++) {
        await act(async () => {
          await result.current.loadWealthData();
        });
      }
      
      // Unmount should clean up properly
      unmount();
      
      // No assertions needed - test passes if no memory leak warnings
    });
  });

  describe('Error Recovery', () => {
    it('should recover from errors and allow retry', async () => {
      const { result } = renderHook(() => useWealthTracker());
      
      // First call fails
      mockedMockApi.getWealthData.mockResolvedValueOnce({
        success: false,
        error: 'Server error',
      });
      
      await act(async () => {
        await result.current.loadWealthData();
      });
      
      expect(result.current.error).toBe('Server error');
      
      // Second call succeeds
      mockedMockApi.getWealthData.mockResolvedValueOnce({
        success: true,
        data: { portfolioData: [], payslipData: [] },
      });
      
      await act(async () => {
        await result.current.loadWealthData();
      });
      
      expect(result.current.error).toBeNull();
      expect(result.current.wealthData).toEqual({ portfolioData: [], payslipData: [] });
    });
  });
});